#!/usr/bin/env python3
"""
Safe Luraph Deobfuscator - ไม่แปลง string เพื่อความปลอดภัย
"""

import re
import sys
import os
from typing import Dict, List, Tuple, Optional

class SafeLuraphDeobfuscator:
    def __init__(self):
        self.constants = {}
        self.functions = {}
        self.variables = {}
        
    def read_file(self, filepath: str) -> str:
        """อ่านไฟล์ Lua"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            with open(filepath, 'r', encoding='latin-1') as f:
                return f.read()
    
    def write_file(self, filepath: str, content: str):
        """เขียนไฟล์ที่ deobfuscate แล้ว"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def decode_only_obvious_escapes(self, content: str) -> str:
        """แปลงเฉพาะ escape sequences ที่ชัดเจนและปลอดภัย"""
        
        def process_string_safely(match):
            """ประมวลผล string อย่างระมัดระวัง"""
            full_string = match.group(0)
            quote_char = full_string[0]
            string_content = full_string[1:-1]
            
            # เก็บ string เดิมไว้
            original = string_content
            
            try:
                # แปลงเฉพาะ escape sequences ที่มั่นใจ 100%
                safe_replacements = {
                    '\\\\': '\\',    # backslash
                    '\\"': '"',      # double quote
                    "\\'": "'",      # single quote
                    '\\n': '\n',     # newline
                    '\\r': '\r',     # carriage return
                    '\\t': '\t',     # tab
                }
                
                for escape, replacement in safe_replacements.items():
                    string_content = string_content.replace(escape, replacement)
                
                # แปลงเฉพาะ hex escapes ที่เป็นตัวอักษรธรรมดา (A-Z, a-z, 0-9)
                def safe_hex_replace(m):
                    try:
                        hex_val = m.group(1)
                        char_code = int(hex_val, 16)
                        # เฉพาะ ASCII printable characters
                        if 32 <= char_code <= 126:
                            char = chr(char_code)
                            # เฉพาะตัวอักษร ตัวเลข และสัญลักษณ์ปกติ
                            if char.isalnum() or char in ' .,!?-_()[]{}':
                                return char
                    except:
                        pass
                    return m.group(0)  # คืนค่าเดิม
                
                # แปลงเฉพาะ hex ที่ปลอดภัย
                string_content = re.sub(r'\\x([0-9a-fA-F]{2})', safe_hex_replace, string_content)
                
            except Exception:
                # ถ้าเกิดข้อผิดพลาด ใช้ string เดิม
                string_content = original
            
            return quote_char + string_content + quote_char
        
        # ประมวลผลเฉพาะ string literals ที่ชัดเจน
        content = re.sub(r'"[^"]*"', process_string_safely, content)
        content = re.sub(r"'[^']*'", process_string_safely, content)
        
        return content
    
    def simplify_numbers_safely(self, content: str) -> str:
        """แปลง numbers อย่างปลอดภัย"""
        
        def safe_hex_to_decimal(match):
            try:
                hex_str = match.group(0)
                decimal = int(hex_str, 16)
                # เฉพาะตัวเลขเล็กๆ ที่มั่นใจว่าไม่ใช่ address หรือ hash
                if 0 <= decimal <= 10000:
                    return str(decimal)
            except:
                pass
            return match.group(0)
        
        def safe_binary_to_decimal(match):
            try:
                binary_str = match.group(0)
                binary_digits = binary_str[2:].replace('_', '')
                decimal = int(binary_digits, 2)
                # เฉพาะตัวเลขเล็กๆ
                if 0 <= decimal <= 10000:
                    return str(decimal)
            except:
                pass
            return match.group(0)
        
        # แปลงเฉพาะตัวเลขที่มั่นใจว่าปลอดภัย
        content = re.sub(r'0[xX][0-9a-fA-F]{1,4}(?![0-9a-fA-F])', safe_hex_to_decimal, content)
        content = re.sub(r'0[bB][01_]{1,16}(?![01_])', safe_binary_to_decimal, content)
        
        return content
    
    def format_code_minimally(self, content: str) -> str:
        """จัดรูปแบบ code เพียงเล็กน้อย"""
        # เพิ่ม newlines เฉพาะจุดที่จำเป็น
        content = re.sub(r';(?=\s*local\s)', ';\n', content)  # หลัง ; ที่ตามด้วย local
        content = re.sub(r';(?=\s*if\s)', ';\n', content)     # หลัง ; ที่ตามด้วย if
        content = re.sub(r';(?=\s*for\s)', ';\n', content)    # หลัง ; ที่ตามด้วย for
        content = re.sub(r';(?=\s*while\s)', ';\n', content)  # หลัง ; ที่ตามด้วย while
        content = re.sub(r';(?=\s*return\s)', ';\n', content) # หลัง ; ที่ตามด้วย return
        
        # เพิ่ม newlines หลัง end
        content = re.sub(r'\bend;', 'end;\n', content)
        
        # เพิ่ม indentation พื้นฐาน
        lines = content.split('\n')
        formatted_lines = []
        indent_level = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # ลด indent สำหรับ end
            if line.startswith('end'):
                indent_level = max(0, indent_level - 1)
            
            # เพิ่ม indentation
            if indent_level > 0:
                formatted_lines.append('    ' * indent_level + line)
            else:
                formatted_lines.append(line)
            
            # เพิ่ม indent สำหรับ keywords
            if any(keyword in line for keyword in ['function', 'if', 'for', 'while', 'do']):
                if not line.strip().endswith('end'):  # ไม่ใช่ one-liner
                    indent_level += 1
        
        return '\n'.join(formatted_lines)
    
    def analyze_structure(self, content: str) -> Dict:
        """วิเคราะห์โครงสร้างของ code"""
        analysis = {
            'total_chars': len(content),
            'total_lines': content.count('\n') + 1,
            'has_return_table': 'return(' in content or 'return {' in content,
            'function_count': len(re.findall(r'function\s*\(', content)),
            'string_count': len(re.findall(r'["\'][^"\']*["\']', content)),
            'hex_numbers': len(re.findall(r'0[xX][0-9a-fA-F]+', content)),
            'binary_numbers': len(re.findall(r'0[bB][01_]+', content)),
            'hex_escapes': len(re.findall(r'\\x[0-9a-fA-F]{2}', content)),
            'unicode_escapes': len(re.findall(r'\\u\{[0-9a-fA-F]+\}', content)),
        }
        return analysis
    
    def deobfuscate(self, content: str) -> str:
        """Main deobfuscation process ที่ปลอดภัย"""
        print("เริ่มต้น Safe Luraph deobfuscation...")
        
        # วิเคราะห์โครงสร้างก่อน
        print("0. กำลังวิเคราะห์โครงสร้าง...")
        analysis = self.analyze_structure(content)
        print(f"   - ขนาดไฟล์: {analysis['total_chars']} characters")
        print(f"   - จำนวนบรรทัด: {analysis['total_lines']}")
        print(f"   - จำนวน functions: {analysis['function_count']}")
        print(f"   - จำนวน strings: {analysis['string_count']}")
        print(f"   - Hex numbers: {analysis['hex_numbers']}")
        print(f"   - Hex escapes: {analysis['hex_escapes']}")
        
        # ขั้นตอนที่ 1: ลบ comment header
        print("1. กำลังลบ obfuscator header...")
        content = re.sub(r'-- This file was protected using.*?\n', '', content)
        
        # ขั้นตอนที่ 2: แปลงเฉพาะ escape sequences ที่ปลอดภัย
        print("2. กำลังแปลง escape sequences ที่ปลอดภัยเท่านั้น...")
        content = self.decode_only_obvious_escapes(content)
        
        # ขั้นตอนที่ 3: แปลงเฉพาะตัวเลขที่ปลอดภัย
        print("3. กำลังแปลงตัวเลขที่ปลอดภัย...")
        content = self.simplify_numbers_safely(content)
        
        # ขั้นตอนที่ 4: จัดรูปแบบเพียงเล็กน้อย
        print("4. กำลังจัดรูปแบบ code เล็กน้อย...")
        content = self.format_code_minimally(content)
        
        print("5. เสร็จสิ้น!")
        
        # วิเคราะห์ผลลัพธ์
        final_analysis = self.analyze_structure(content)
        print(f"   - ผลลัพธ์: {final_analysis['total_lines']} บรรทัด")
        
        return content

def main():
    if len(sys.argv) < 2:
        print("การใช้งาน: python safe_luraph_deobfuscator.py <input_file> [output_file]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else 'safe_deobfuscated.lua'
    
    if not os.path.exists(input_file):
        print(f"ไม่พบไฟล์: {input_file}")
        sys.exit(1)
    
    deobfuscator = SafeLuraphDeobfuscator()
    
    print(f"กำลังอ่านไฟล์: {input_file}")
    content = deobfuscator.read_file(input_file)
    
    deobfuscated_content = deobfuscator.deobfuscate(content)
    
    print(f"กำลังเขียนไฟล์: {output_file}")
    deobfuscator.write_file(output_file, deobfuscated_content)
    
    print("เสร็จสิ้น!")
    print(f"ไฟล์ที่ deobfuscate แล้ว: {output_file}")

if __name__ == "__main__":
    main()
