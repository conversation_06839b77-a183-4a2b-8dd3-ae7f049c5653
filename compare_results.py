#!/usr/bin/env python3
"""
เปรียบเทียบผลลัพธ์ของ deobfuscator ต่างๆ
"""

import os
import re

def analyze_file(filepath):
    """วิเคราะห์ไฟล์"""
    if not os.path.exists(filepath):
        return None
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    analysis = {
        'file_size': len(content),
        'line_count': content.count('\n') + 1,
        'function_count': len(re.findall(r'function\s*\(', content)),
        'string_count': len(re.findall(r'["\'][^"\']*["\']', content)),
        'hex_escapes': len(re.findall(r'\\x[0-9a-fA-F]{2}', content)),
        'unicode_escapes': len(re.findall(r'\\u\{[0-9a-fA-F]+\}', content)),
        'octal_escapes': len(re.findall(r'\\[0-7]{1,3}', content)),
        'hex_numbers': len(re.findall(r'0[xX][0-9a-fA-F]+', content)),
        'binary_numbers': len(re.findall(r'0[bB][01_]+', content)),
        'broken_strings': 0,  # จะนับใน function อื่น
    }
    
    # ตรวจสอบ string ที่พัง
    strings = re.findall(r'["\'][^"\']*["\']', content)
    broken_count = 0
    for string in strings:
        # ตรวจสอบว่ามี character ที่ไม่ควรมีใน string หรือไม่
        if re.search(r'[^\x20-\x7E\n\r\t]', string[1:-1]):  # non-printable chars
            broken_count += 1
    
    analysis['broken_strings'] = broken_count
    
    return analysis

def compare_deobfuscators():
    """เปรียบเทียบผลลัพธ์ของ deobfuscator ต่างๆ"""
    
    files_to_compare = {
        'Original': 'obfuscated.lua',
        'Luraph Deobfuscator': 'test_output.lua',
        'General Deobfuscator': 'general_output.lua',
        'Improved Deobfuscator': 'improved_output.lua',
        'Safe Deobfuscator': 'safe_output.lua'
    }
    
    print("=" * 80)
    print("เปรียบเทียบผลลัพธ์ Lua Deobfuscator")
    print("=" * 80)
    
    results = {}
    
    for name, filepath in files_to_compare.items():
        analysis = analyze_file(filepath)
        if analysis:
            results[name] = analysis
            print(f"\n📁 {name} ({filepath}):")
            print(f"   ขนาดไฟล์: {analysis['file_size']:,} characters")
            print(f"   จำนวนบรรทัด: {analysis['line_count']:,}")
            print(f"   Functions: {analysis['function_count']}")
            print(f"   Strings: {analysis['string_count']}")
            print(f"   Hex escapes: {analysis['hex_escapes']}")
            print(f"   Unicode escapes: {analysis['unicode_escapes']}")
            print(f"   Octal escapes: {analysis['octal_escapes']}")
            print(f"   Hex numbers: {analysis['hex_numbers']}")
            print(f"   Binary numbers: {analysis['binary_numbers']}")
            print(f"   Broken strings: {analysis['broken_strings']}")
        else:
            print(f"\n❌ {name}: ไม่พบไฟล์ {filepath}")
    
    # สรุปผลการเปรียบเทียบ
    print("\n" + "=" * 80)
    print("สรุปผลการเปรียบเทียบ")
    print("=" * 80)
    
    if 'Original' in results:
        original = results['Original']
        print(f"ไฟล์ต้นฉบับ: {original['file_size']:,} chars, {original['line_count']} lines")
        
        for name, analysis in results.items():
            if name == 'Original':
                continue
            
            print(f"\n🔧 {name}:")
            
            # คำนวณการลดลงของ obfuscation
            reduction_hex = original['hex_escapes'] - analysis['hex_escapes']
            reduction_unicode = original['unicode_escapes'] - analysis['unicode_escapes']
            reduction_hex_nums = original['hex_numbers'] - analysis['hex_numbers']
            
            print(f"   ✅ ลด hex escapes: {reduction_hex} ({reduction_hex/original['hex_escapes']*100:.1f}%)")
            print(f"   ✅ ลด unicode escapes: {reduction_unicode} ({reduction_unicode/original['unicode_escapes']*100:.1f}%)")
            print(f"   ✅ ลด hex numbers: {reduction_hex_nums} ({reduction_hex_nums/original['hex_numbers']*100:.1f}%)")
            print(f"   📊 เพิ่มบรรทัด: {analysis['line_count'] - original['line_count']:,}")
            
            # ประเมินคุณภาพ
            if analysis['broken_strings'] == 0:
                print("   ✅ String quality: ดีเยี่ยม (ไม่มี string พัง)")
            elif analysis['broken_strings'] < 10:
                print(f"   ⚠️  String quality: ดี (string พัง {analysis['broken_strings']} ตัว)")
            else:
                print(f"   ❌ String quality: ต้องปรับปรุง (string พัง {analysis['broken_strings']} ตัว)")
    
    # แนะนำ
    print("\n" + "=" * 80)
    print("คำแนะนำ")
    print("=" * 80)
    
    best_deobfuscator = None
    best_score = -1
    
    for name, analysis in results.items():
        if name == 'Original':
            continue
        
        # คำนวณคะแนน (ยิ่งน้อยยิ่งดี)
        score = analysis['broken_strings'] * 10 + analysis['hex_escapes'] + analysis['unicode_escapes']
        
        if best_deobfuscator is None or score < best_score:
            best_deobfuscator = name
            best_score = score
    
    if best_deobfuscator:
        print(f"🏆 แนะนำ: {best_deobfuscator}")
        print(f"   - มี string พังน้อยที่สุด")
        print(f"   - แปลง obfuscation ได้ดี")
        print(f"   - เหมาะสำหรับการใช้งานจริง")
    
    print(f"\n📝 หมายเหตุ:")
    print(f"   - Safe Deobfuscator เน้นความปลอดภัย (string ไม่พัง)")
    print(f"   - Luraph Deobfuscator แปลงได้มากที่สุดแต่อาจมี string พัง")
    print(f"   - General Deobfuscator เหมาะสำหรับ obfuscator ทั่วไป")

def show_sample_strings():
    """แสดงตัวอย่าง string จากแต่ละ deobfuscator"""
    
    print("\n" + "=" * 80)
    print("ตัวอย่าง String จากแต่ละ Deobfuscator")
    print("=" * 80)
    
    files = {
        'Safe Deobfuscator': 'safe_output.lua',
        'Luraph Deobfuscator': 'test_output.lua'
    }
    
    for name, filepath in files.items():
        if os.path.exists(filepath):
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # หา string ตัวอย่าง
            strings = re.findall(r'["\'][^"\']{10,50}["\']', content)[:5]
            
            print(f"\n📄 {name}:")
            for i, string in enumerate(strings, 1):
                print(f"   {i}. {string}")

if __name__ == "__main__":
    compare_deobfuscators()
    show_sample_strings()
