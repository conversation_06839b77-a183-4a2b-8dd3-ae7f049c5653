#!/usr/bin/env python3
"""
Enhanced Safe Luraph Deobfuscator - decode string ได้มากขึ้น และจัดรูปแบบดีขึ้น
"""

import re
import sys
import os
import base64
from typing import Dict, List, Tuple, Optional

class EnhancedSafeDeobfuscator:
    def __init__(self):
        self.constants = {}
        self.functions = {}
        self.variables = {}
        
    def read_file(self, filepath: str) -> str:
        """อ่านไฟล์ Lua"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            with open(filepath, 'r', encoding='latin-1') as f:
                return f.read()
    
    def write_file(self, filepath: str, content: str):
        """เขียนไฟล์ที่ deobfuscate แล้ว"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def enhanced_decode_strings(self, content: str) -> str:
        """Decode strings อย่างปลอดภัยแต่ครอบคลุมมากขึ้น"""
        
        def safe_decode_string(match):
            """Decode string literal อย่างปลอดภัย"""
            full_string = match.group(0)
            quote_char = full_string[0]
            string_content = full_string[1:-1]
            original_content = string_content
            
            try:
                # 1. แปลง hex escapes (\x41 -> A)
                def hex_replacer(m):
                    try:
                        hex_val = m.group(1)
                        char_code = int(hex_val, 16)
                        if 0 <= char_code <= 255:
                            char = chr(char_code)
                            # อนุญาตตัวอักษร ตัวเลข และสัญลักษณ์ปกติ
                            if char.isprintable() or char in '\n\r\t':
                                return char
                    except:
                        pass
                    return m.group(0)
                
                # 2. แปลง unicode escapes (\u{41} -> A)
                def unicode_replacer(m):
                    try:
                        unicode_val = m.group(1)
                        char_code = int(unicode_val, 16)
                        if 0 <= char_code <= 0x10FFFF:
                            char = chr(char_code)
                            if char.isprintable() or char in '\n\r\t':
                                return char
                    except:
                        pass
                    return m.group(0)
                
                # 3. แปลง octal escapes (\101 -> A)
                def octal_replacer(m):
                    try:
                        octal_val = m.group(1)
                        if len(octal_val) <= 3 and all(c in '01234567' for c in octal_val):
                            char_code = int(octal_val, 8)
                            if 0 <= char_code <= 255:
                                char = chr(char_code)
                                if char.isprintable() or char in '\n\r\t':
                                    return char
                    except:
                        pass
                    return m.group(0)
                
                # แปลง escape sequences
                string_content = re.sub(r'\\x([0-9a-fA-F]{2})', hex_replacer, string_content)
                string_content = re.sub(r'\\u\{([0-9a-fA-F]+)\}', unicode_replacer, string_content)
                string_content = re.sub(r'\\([0-7]{1,3})', octal_replacer, string_content)
                
                # แปลง basic escapes
                basic_escapes = {
                    '\\\\': '\\',
                    '\\"': '"',
                    "\\'": "'",
                    '\\n': '\n',
                    '\\r': '\r',
                    '\\t': '\t',
                    '\\a': '\a',
                    '\\b': '\b',
                    '\\f': '\f',
                    '\\v': '\v'
                }
                
                for escape, replacement in basic_escapes.items():
                    string_content = string_content.replace(escape, replacement)
                
                # จัดการ \z (skip whitespace)
                string_content = re.sub(r'\\z\s*', '', string_content)
                
            except Exception:
                # ถ้าเกิดข้อผิดพลาด ใช้ string เดิม
                string_content = original_content
            
            return quote_char + string_content + quote_char
        
        # ประมวลผล string literals
        content = re.sub(r'"[^"]*"', safe_decode_string, content)
        content = re.sub(r"'[^']*'", safe_decode_string, content)
        
        return content
    
    def find_and_decode_large_strings(self, content: str) -> str:
        """หาและ decode กลุ่มก้อน string ขนาดใหญ่ที่อาจเป็น code"""
        
        # หา string ที่ยาวมากๆ (อาจเป็น encoded code)
        large_string_pattern = r'["\'][^"\']{100,}["\']'
        large_strings = re.findall(large_string_pattern, content)
        
        print(f"   พบ large strings: {len(large_strings)} ตัว")
        
        for large_string in large_strings:
            original = large_string
            decoded = large_string
            
            # ลองหลายวิธีในการ decode
            try:
                # วิธีที่ 1: ลอง decode เป็น base64
                inner_content = large_string[1:-1]  # ลบ quotes
                try:
                    base64_decoded = base64.b64decode(inner_content).decode('utf-8')
                    if self.looks_like_lua_code(base64_decoded):
                        print(f"   พบ base64 encoded code!")
                        decoded = f'--[[ BASE64 DECODED CODE:\n{base64_decoded}\n--]]'
                        content = content.replace(original, decoded)
                        continue
                except:
                    pass
                
                # วิธีที่ 2: ลอง decode escape sequences ทั้งหมด
                temp_decoded = inner_content
                
                # แปลง hex escapes
                temp_decoded = re.sub(r'\\x([0-9a-fA-F]{2})', 
                                    lambda m: chr(int(m.group(1), 16)) if int(m.group(1), 16) < 256 else m.group(0), 
                                    temp_decoded)
                
                # แปลง unicode escapes
                temp_decoded = re.sub(r'\\u\{([0-9a-fA-F]+)\}', 
                                    lambda m: chr(int(m.group(1), 16)) if int(m.group(1), 16) < 0x10FFFF else m.group(0), 
                                    temp_decoded)
                
                # แปลง octal escapes
                temp_decoded = re.sub(r'\\([0-7]{1,3})', 
                                    lambda m: chr(int(m.group(1), 8)) if int(m.group(1), 8) < 256 else m.group(0), 
                                    temp_decoded)
                
                if self.looks_like_lua_code(temp_decoded) and temp_decoded != inner_content:
                    print(f"   พบ escape encoded code!")
                    formatted_code = self.format_embedded_code(temp_decoded)
                    decoded = f'--[[ DECODED CODE:\n{formatted_code}\n--]]'
                    content = content.replace(original, decoded)
                
            except Exception as e:
                print(f"   ไม่สามารถ decode large string: {str(e)}")
                continue
        
        return content
    
    def looks_like_lua_code(self, text: str) -> bool:
        """ตรวจสอบว่า text ดูเหมือน Lua code หรือไม่"""
        lua_keywords = ['function', 'local', 'if', 'then', 'else', 'end', 'for', 'while', 'do', 'return']
        lua_patterns = [r'\bfunction\s*\(', r'\blocal\s+\w+', r'\bif\s+.+\bthen\b', r'\breturn\b']
        
        # ตรวจสอบ keywords
        keyword_count = sum(1 for keyword in lua_keywords if keyword in text.lower())
        
        # ตรวจสอบ patterns
        pattern_count = sum(1 for pattern in lua_patterns if re.search(pattern, text))
        
        # ตรวจสอบ syntax characters
        syntax_chars = ['(', ')', '{', '}', '[', ']', '=', ';']
        syntax_count = sum(1 for char in syntax_chars if char in text)
        
        return keyword_count >= 2 or pattern_count >= 1 or (syntax_count >= 5 and len(text) > 50)
    
    def format_embedded_code(self, code: str) -> str:
        """จัดรูปแบบ embedded code"""
        # เพิ่ม newlines หลัง semicolons
        code = re.sub(r';(?=\s*\w)', ';\n', code)
        
        # เพิ่ม newlines หลัง keywords
        code = re.sub(r'\b(function|if|for|while)\b([^;]+);', r'\1\2;\n', code)
        
        # จัด indentation พื้นฐาน
        lines = code.split('\n')
        formatted_lines = []
        indent_level = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if any(keyword in line for keyword in ['end', '}', '])']):
                indent_level = max(0, indent_level - 1)
            
            formatted_lines.append('    ' * indent_level + line)
            
            if any(keyword in line for keyword in ['function', 'if', 'for', 'while', 'do', '{', '[']):
                if not line.strip().endswith('end'):
                    indent_level += 1
        
        return '\n'.join(formatted_lines)
    
    def clean_format_code(self, content: str) -> str:
        """จัดรูปแบบ code ให้ไม่มี tab และอ่านง่าย"""
        
        # 1. แทนที่ tab ด้วย spaces
        content = content.replace('\t', '    ')
        
        # 2. เพิ่ม newlines ในจุดที่เหมาะสม
        content = re.sub(r';(?=\s*local\s)', ';\n', content)
        content = re.sub(r';(?=\s*if\s)', ';\n', content)
        content = re.sub(r';(?=\s*for\s)', ';\n', content)
        content = re.sub(r';(?=\s*while\s)', ';\n', content)
        content = re.sub(r';(?=\s*return\s)', ';\n', content)
        content = re.sub(r';(?=\s*end\b)', ';\n', content)
        
        # 3. จัดการ function definitions
        content = re.sub(r'(\w+)=function\(', r'\1 = function(', content)
        content = re.sub(r'function\([^)]*\)', lambda m: m.group(0) + '\n', content)
        
        # 4. จัดการ control structures
        content = re.sub(r'\bif\s+([^;]+);', r'if \1;\n', content)
        content = re.sub(r'\bfor\s+([^;]+);', r'for \1;\n', content)
        content = re.sub(r'\bwhile\s+([^;]+);', r'while \1;\n', content)
        
        # 5. จัดการ end statements
        content = re.sub(r'\bend;', 'end;\n', content)
        content = re.sub(r'\bend,', 'end,\n', content)
        
        # 6. ลบ excessive whitespace
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        content = re.sub(r'[ ]+', ' ', content)
        
        # 7. จัด indentation ใหม่
        lines = content.split('\n')
        formatted_lines = []
        indent_level = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # ลด indent สำหรับ closing statements
            if any(line.startswith(keyword) for keyword in ['end', '}', '])', 'else', 'elseif']):
                indent_level = max(0, indent_level - 1)
            
            # เพิ่ม indentation
            formatted_lines.append('    ' * indent_level + line)
            
            # เพิ่ม indent สำหรับ opening statements
            if any(keyword in line for keyword in ['function(', 'if ', 'for ', 'while ', 'do', '{', 'then']):
                if not any(line.endswith(keyword) for keyword in ['end', 'end;', 'end,']):
                    indent_level += 1
        
        return '\n'.join(formatted_lines)
    
    def deobfuscate(self, content: str) -> str:
        """Main deobfuscation process ที่ปรับปรุงแล้ว"""
        print("เริ่มต้น Enhanced Safe Luraph deobfuscation...")
        
        # ขั้นตอนที่ 1: ลบ comment header
        print("1. กำลังลบ obfuscator header...")
        content = re.sub(r'-- This file was protected using.*?\n', '', content)
        
        # ขั้นตอนที่ 2: หาและ decode กลุ่มก้อน string ขนาดใหญ่
        print("2. กำลังหาและ decode กลุ่มก้อน string ขนาดใหญ่...")
        content = self.find_and_decode_large_strings(content)
        
        # ขั้นตอนที่ 3: decode strings ทั่วไป
        print("3. กำลัง decode strings...")
        content = self.enhanced_decode_strings(content)
        
        # ขั้นตอนที่ 4: แปลงตัวเลข
        print("4. กำลังแปลงตัวเลข...")
        content = self.simplify_numbers_safely(content)
        
        # ขั้นตอนที่ 5: จัดรูปแบบ code
        print("5. กำลังจัดรูปแบบ code...")
        content = self.clean_format_code(content)
        
        print("6. เสร็จสิ้น!")
        
        return content
    
    def simplify_numbers_safely(self, content: str) -> str:
        """แปลง numbers อย่างปลอดภัย"""
        def safe_hex_to_decimal(match):
            try:
                hex_str = match.group(0)
                decimal = int(hex_str, 16)
                if 0 <= decimal <= 10000:
                    return str(decimal)
            except:
                pass
            return match.group(0)
        
        def safe_binary_to_decimal(match):
            try:
                binary_str = match.group(0)
                binary_digits = binary_str[2:].replace('_', '')
                decimal = int(binary_digits, 2)
                if 0 <= decimal <= 10000:
                    return str(decimal)
            except:
                pass
            return match.group(0)
        
        content = re.sub(r'0[xX][0-9a-fA-F]{1,4}(?![0-9a-fA-F])', safe_hex_to_decimal, content)
        content = re.sub(r'0[bB][01_]{1,16}(?![01_])', safe_binary_to_decimal, content)
        
        return content

def main():
    if len(sys.argv) < 2:
        print("การใช้งาน: python enhanced_safe_deobfuscator.py <input_file> [output_file]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else 'enhanced_deobfuscated.lua'
    
    if not os.path.exists(input_file):
        print(f"ไม่พบไฟล์: {input_file}")
        sys.exit(1)
    
    deobfuscator = EnhancedSafeDeobfuscator()
    
    print(f"กำลังอ่านไฟล์: {input_file}")
    content = deobfuscator.read_file(input_file)
    
    deobfuscated_content = deobfuscator.deobfuscate(content)
    
    print(f"กำลังเขียนไฟล์: {output_file}")
    deobfuscator.write_file(output_file, deobfuscated_content)
    
    print("เสร็จสิ้น!")
    print(f"ไฟล์ที่ deobfuscate แล้ว: {output_file}")

if __name__ == "__main__":
    main()
