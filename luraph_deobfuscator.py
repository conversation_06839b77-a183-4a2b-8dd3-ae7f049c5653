#!/usr/bin/env python3
"""
Luraph Deobfuscator - เฉพาะสำหรับ Luraph Obfuscator
"""

import re
import sys
import os
import json
import base64
from typing import Dict, List, Tuple, Optional

class LuraphDeobfuscator:
    def __init__(self):
        self.constants = {}
        self.functions = {}
        self.variables = {}
        
    def read_file(self, filepath: str) -> str:
        """อ่านไฟล์ Lua"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            with open(filepath, 'r', encoding='latin-1') as f:
                return f.read()
    
    def write_file(self, filepath: str, content: str):
        """เขียนไฟล์ที่ deobfuscate แล้ว"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def decode_string_escapes(self, content: str) -> str:
        """แปลง string escape sequences"""
        # แปลง \x hex codes
        def hex_replacer(match):
            try:
                return chr(int(match.group(1), 16))
            except ValueError:
                return match.group(0)
        
        # แปลง \u{} unicode codes
        def unicode_replacer(match):
            try:
                return chr(int(match.group(1), 16))
            except ValueError:
                return match.group(0)
        
        # แปลง \ddd octal codes
        def octal_replacer(match):
            try:
                return chr(int(match.group(1), 8))
            except ValueError:
                return match.group(0)
        
        content = re.sub(r'\\x([0-9a-fA-F]{2})', hex_replacer, content)
        content = re.sub(r'\\u\{([0-9a-fA-F]+)\}', unicode_replacer, content)
        content = re.sub(r'\\([0-7]{1,3})', octal_replacer, content)
        
        # แปลง escape sequences อื่นๆ
        content = content.replace('\\n', '\n')
        content = content.replace('\\r', '\r')
        content = content.replace('\\t', '\t')
        content = content.replace('\\z', '')  # \z ใน Lua คือ skip whitespace
        
        return content
    
    def extract_string_constants(self, content: str) -> Dict[str, str]:
        """แยก string constants ออกมา"""
        constants = {}
        
        # หา string literals ในรูปแบบต่างๆ
        patterns = [
            r"'([^'\\]|\\.)*'",  # single quotes
            r'"([^"\\]|\\.)*"',  # double quotes
            r'\[=*\[.*?\]=*\]',  # long strings
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, content, re.DOTALL)
            for match in matches:
                original = match.group(0)
                decoded = self.decode_string_escapes(original)
                if original != decoded:
                    constants[original] = decoded
        
        return constants
    
    def analyze_luraph_structure(self, content: str) -> Dict:
        """วิเคราะห์โครงสร้างของ Luraph obfuscated code"""
        analysis = {
            'has_return_table': False,
            'main_table_keys': [],
            'function_patterns': [],
            'constant_patterns': []
        }
        
        # ตรวจสอบว่ามี return table หรือไม่
        if re.search(r'return\s*\(\s*\{', content):
            analysis['has_return_table'] = True
        
        # หา function keys ใน table
        function_keys = re.findall(r'(\w+)\s*=\s*function\s*\(', content)
        analysis['main_table_keys'].extend(function_keys)
        
        # หา patterns ของ constants
        hex_patterns = re.findall(r'0[xX][0-9a-fA-F]+', content)
        analysis['constant_patterns'].extend(hex_patterns)
        
        return analysis
    
    def extract_main_functions(self, content: str) -> Dict[str, str]:
        """แยก main functions ออกมา"""
        functions = {}
        
        # หา function definitions
        pattern = r'(\w+)\s*=\s*function\s*\([^)]*\)(.*?)(?=\w+\s*=\s*function|\}|$)'
        matches = re.finditer(pattern, content, re.DOTALL)
        
        for match in matches:
            func_name = match.group(1)
            func_body = match.group(2)
            functions[func_name] = func_body.strip()
        
        return functions
    
    def simplify_hex_numbers(self, content: str) -> str:
        """แปลง hex numbers เป็น decimal"""
        def hex_to_decimal(match):
            hex_str = match.group(0)
            try:
                decimal = int(hex_str, 16)
                return str(decimal)
            except ValueError:
                return hex_str
        
        # แปลง 0x... และ 0X... เป็น decimal
        content = re.sub(r'0[xX][0-9a-fA-F]+', hex_to_decimal, content)
        
        return content
    
    def simplify_binary_numbers(self, content: str) -> str:
        """แปลง binary numbers เป็น decimal"""
        def binary_to_decimal(match):
            binary_str = match.group(0)
            try:
                # ลบ 0b หรือ 0B prefix
                binary_digits = binary_str[2:]
                decimal = int(binary_digits, 2)
                return str(decimal)
            except ValueError:
                return binary_str
        
        # แปลง 0b... และ 0B... เป็น decimal
        content = re.sub(r'0[bB][01_]+', binary_to_decimal, content)
        
        return content
    
    def clean_obfuscated_names(self, content: str) -> str:
        """ทำความสะอาด obfuscated variable names"""
        # แทนที่ variable names ที่เป็น single character หรือ confusing
        confusing_vars = ['C', 'M', 'g', 'U', 'a', 'K', 'z', 'i', 'G', 's', 'b', 'I', 'y', 'n']
        
        for i, var in enumerate(confusing_vars):
            # แทนที่ด้วยชื่อที่มีความหมาย
            new_name = f'var_{i+1}'
            # ใช้ word boundary เพื่อไม่ให้แทนที่ในกรณีที่ไม่ควร
            content = re.sub(rf'\b{re.escape(var)}\b', new_name, content)
        
        return content
    
    def format_code(self, content: str) -> str:
        """จัดรูปแบบ code ให้อ่านง่าย"""
        # เพิ่ม newlines
        content = re.sub(r';', ';\n', content)
        content = re.sub(r'\{', '{\n', content)
        content = re.sub(r'\}', '\n}', content)
        content = re.sub(r'\bdo\b', 'do\n', content)
        content = re.sub(r'\bthen\b', 'then\n', content)
        content = re.sub(r'\bend\b', '\nend', content)
        
        # ลบ empty lines ที่เกินจำเป็น
        content = re.sub(r'\n\s*\n', '\n', content)
        
        # เพิ่ม indentation พื้นฐาน
        lines = content.split('\n')
        formatted_lines = []
        indent_level = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # ลด indent สำหรับ closing brackets
            if line.startswith('}') or line.startswith('end'):
                indent_level = max(0, indent_level - 1)
            
            # เพิ่ม indentation
            formatted_lines.append('    ' * indent_level + line)
            
            # เพิ่ม indent สำหรับ opening brackets
            if line.endswith('{') or line.endswith('do') or line.endswith('then'):
                indent_level += 1
        
        return '\n'.join(formatted_lines)
    
    def deobfuscate(self, content: str) -> str:
        """Main deobfuscation process"""
        print("เริ่มต้น Luraph deobfuscation...")
        
        # ขั้นตอนที่ 1: แปลง string escapes
        print("1. กำลังแปลง string escape sequences...")
        content = self.decode_string_escapes(content)
        
        # ขั้นตอนที่ 2: แปลง hex และ binary numbers
        print("2. กำลังแปลง hex และ binary numbers...")
        content = self.simplify_hex_numbers(content)
        content = self.simplify_binary_numbers(content)
        
        # ขั้นตอนที่ 3: วิเคราะห์โครงสร้าง
        print("3. กำลังวิเคราะห์โครงสร้าง...")
        analysis = self.analyze_luraph_structure(content)
        print(f"   - พบ return table: {analysis['has_return_table']}")
        print(f"   - พบ function keys: {len(analysis['main_table_keys'])}")
        
        # ขั้นตอนที่ 4: ลบ comment header
        print("4. กำลังลบ obfuscator header...")
        content = re.sub(r'-- This file was protected using.*?\n', '', content)
        
        # ขั้นตอนที่ 5: ทำความสะอาด variable names
        print("5. กำลังทำความสะอาด variable names...")
        content = self.clean_obfuscated_names(content)
        
        # ขั้นตอนที่ 6: จัดรูปแบบ code
        print("6. กำลังจัดรูปแบบ code...")
        content = self.format_code(content)
        
        return content

def main():
    if len(sys.argv) < 2:
        print("การใช้งาน: python luraph_deobfuscator.py <input_file> [output_file]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else 'deobfuscated_luraph.lua'
    
    if not os.path.exists(input_file):
        print(f"ไม่พบไฟล์: {input_file}")
        sys.exit(1)
    
    deobfuscator = LuraphDeobfuscator()
    
    print(f"กำลังอ่านไฟล์: {input_file}")
    content = deobfuscator.read_file(input_file)
    
    # ตรวจสอบว่าเป็น Luraph หรือไม่
    if "Luraph Obfuscator" not in content:
        print("คำเตือน: ไฟล์นี้อาจไม่ได้ถูก obfuscate ด้วย Luraph")
    
    deobfuscated_content = deobfuscator.deobfuscate(content)
    
    print(f"กำลังเขียนไฟล์: {output_file}")
    deobfuscator.write_file(output_file, deobfuscated_content)
    
    print("เสร็จสิ้น!")
    print(f"ไฟล์ที่ deobfuscate แล้ว: {output_file}")

if __name__ == "__main__":
    main()
