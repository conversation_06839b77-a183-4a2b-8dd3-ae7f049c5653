#!/usr/bin/env python3
"""
ตัวอย่างการใช้งาน Lua Deobfuscator
"""

import os
import sys
from lua_deobfuscator import LuaDeobfuscator
from luraph_deobfuscator import LuraphDeobfuscator

def demo_general_deobfuscator():
    """ตัวอย่างการใช้งาน General Deobfuscator"""
    print("=== Demo General Deobfuscator ===")
    
    # สร้าง sample obfuscated code
    sample_code = '''
    -- Obfuscated Lua Code
    local \\x61\\x62\\x63 = "\\x48\\x65\\x6C\\x6C\\x6F"
    local \\u{64}\\u{65}\\u{66} = "\\u{57}\\u{6F}\\u{72}\\u{6C}\\u{64}"
    print(\\x61\\x62\\x63 .. " " .. \\u{64}\\u{65}\\u{66})
    '''
    
    # เขียนไฟล์ตัวอย่าง
    with open('sample_obfuscated.lua', 'w') as f:
        f.write(sample_code)
    
    # ใช้ deobfuscator
    deobfuscator = LuaDeobfuscator()
    content = deobfuscator.read_file('sample_obfuscated.lua')
    deobfuscated = deobfuscator.deobfuscate(content)
    
    print("Original:")
    print(sample_code)
    print("\nDeobfuscated:")
    print(deobfuscated)
    
    # ลบไฟล์ตัวอย่าง
    os.remove('sample_obfuscated.lua')

def demo_luraph_deobfuscator():
    """ตัวอย่างการใช้งาน Luraph Deobfuscator"""
    print("\n=== Demo Luraph Deobfuscator ===")
    
    if not os.path.exists('obfuscated.lua'):
        print("ไม่พบไฟล์ obfuscated.lua")
        return
    
    deobfuscator = LuraphDeobfuscator()
    content = deobfuscator.read_file('obfuscated.lua')
    
    print(f"ขนาดไฟล์ต้นฉบับ: {len(content)} characters")
    
    # วิเคราะห์โครงสร้าง
    analysis = deobfuscator.analyze_luraph_structure(content)
    print(f"การวิเคราะห์:")
    print(f"  - มี return table: {analysis['has_return_table']}")
    print(f"  - จำนวน function keys: {len(analysis['main_table_keys'])}")
    
    # Deobfuscate
    deobfuscated = deobfuscator.deobfuscate(content)
    print(f"ขนาดไฟล์หลัง deobfuscate: {len(deobfuscated)} characters")
    
    # เขียนไฟล์ผลลัพธ์
    deobfuscator.write_file('demo_output.lua', deobfuscated)
    print("บันทึกผลลัพธ์ไว้ที่: demo_output.lua")

def compare_methods():
    """เปรียบเทียบวิธีการ deobfuscate ต่างๆ"""
    print("\n=== เปรียบเทียบวิธีการ ===")
    
    if not os.path.exists('obfuscated.lua'):
        print("ไม่พบไฟล์ obfuscated.lua")
        return
    
    # อ่านไฟล์ต้นฉบับ
    with open('obfuscated.lua', 'r', encoding='utf-8') as f:
        original = f.read()
    
    print(f"ไฟล์ต้นฉบับ: {len(original)} characters")
    
    # ทดสอบ General Deobfuscator
    general_deobfuscator = LuaDeobfuscator()
    general_result = general_deobfuscator.deobfuscate(original)
    print(f"General Deobfuscator: {len(general_result)} characters")
    
    # ทดสอบ Luraph Deobfuscator
    luraph_deobfuscator = LuraphDeobfuscator()
    luraph_result = luraph_deobfuscator.deobfuscate(original)
    print(f"Luraph Deobfuscator: {len(luraph_result)} characters")
    
    # บันทึกผลลัพธ์
    with open('general_result.lua', 'w', encoding='utf-8') as f:
        f.write(general_result)
    
    with open('luraph_result.lua', 'w', encoding='utf-8') as f:
        f.write(luraph_result)
    
    print("บันทึกผลลัพธ์:")
    print("  - general_result.lua")
    print("  - luraph_result.lua")

def analyze_obfuscation_techniques():
    """วิเคราะห์เทคนิค obfuscation ที่ใช้"""
    print("\n=== วิเคราะห์เทคนิค Obfuscation ===")
    
    if not os.path.exists('obfuscated.lua'):
        print("ไม่พบไฟล์ obfuscated.lua")
        return
    
    with open('obfuscated.lua', 'r', encoding='utf-8') as f:
        content = f.read()
    
    techniques = {
        'Hex Encoding': len(re.findall(r'\\x[0-9a-fA-F]{2}', content)),
        'Unicode Encoding': len(re.findall(r'\\u\{[0-9a-fA-F]+\}', content)),
        'Octal Encoding': len(re.findall(r'\\[0-7]{1,3}', content)),
        'Hex Numbers': len(re.findall(r'0[xX][0-9a-fA-F]+', content)),
        'Binary Numbers': len(re.findall(r'0[bB][01_]+', content)),
        'String Concatenation': content.count('..'),
        'Function Calls': content.count('('),
        'Table Access': content.count('['),
    }
    
    print("เทคนิค obfuscation ที่พบ:")
    for technique, count in techniques.items():
        if count > 0:
            print(f"  - {technique}: {count} ครั้ง")

def interactive_mode():
    """โหมดโต้ตอบ"""
    print("\n=== โหมดโต้ตอบ ===")
    
    while True:
        print("\nเลือกการทำงาน:")
        print("1. Deobfuscate ไฟล์")
        print("2. วิเคราะห์ไฟล์")
        print("3. เปรียบเทียบวิธีการ")
        print("4. ออกจากโปรแกรม")
        
        choice = input("เลือก (1-4): ").strip()
        
        if choice == '1':
            filename = input("ชื่อไฟล์ input: ").strip()
            if not os.path.exists(filename):
                print(f"ไม่พบไฟล์: {filename}")
                continue
            
            output = input("ชื่อไฟล์ output (Enter = auto): ").strip()
            if not output:
                output = 'deobfuscated_' + filename
            
            # ตรวจสอบประเภท obfuscator
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "Luraph Obfuscator" in content:
                deobfuscator = LuraphDeobfuscator()
                print("ใช้ Luraph Deobfuscator")
            else:
                deobfuscator = LuaDeobfuscator()
                print("ใช้ General Deobfuscator")
            
            result = deobfuscator.deobfuscate(content)
            
            with open(output, 'w', encoding='utf-8') as f:
                f.write(result)
            
            print(f"เสร็จสิ้น! บันทึกไว้ที่: {output}")
        
        elif choice == '2':
            filename = input("ชื่อไฟล์ที่จะวิเคราะห์: ").strip()
            if not os.path.exists(filename):
                print(f"ไม่พบไฟล์: {filename}")
                continue
            
            analyze_file(filename)
        
        elif choice == '3':
            compare_methods()
        
        elif choice == '4':
            print("ขอบคุณที่ใช้งาน!")
            break
        
        else:
            print("กรุณาเลือก 1-4")

def analyze_file(filename):
    """วิเคราะห์ไฟล์"""
    import re
    
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"\nการวิเคราะห์ไฟล์: {filename}")
    print(f"ขนาด: {len(content)} characters")
    print(f"จำนวนบรรทัด: {content.count(chr(10)) + 1}")
    
    # ตรวจสอบประเภท obfuscator
    if "Luraph Obfuscator" in content:
        print("ประเภท: Luraph Obfuscator")
    elif "loadstring" in content:
        print("ประเภท: Loadstring-based")
    else:
        print("ประเภท: ไม่ทราบ/อื่นๆ")
    
    # นับเทคนิค obfuscation
    techniques = {
        'Hex strings': len(re.findall(r'\\x[0-9a-fA-F]{2}', content)),
        'Unicode strings': len(re.findall(r'\\u\{[0-9a-fA-F]+\}', content)),
        'Hex numbers': len(re.findall(r'0[xX][0-9a-fA-F]+', content)),
        'Binary numbers': len(re.findall(r'0[bB][01_]+', content)),
    }
    
    print("เทคนิค obfuscation:")
    for tech, count in techniques.items():
        if count > 0:
            print(f"  - {tech}: {count}")

if __name__ == "__main__":
    import re
    
    print("🔧 Lua Deobfuscator - ตัวอย่างการใช้งาน")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_mode()
    else:
        # รันตัวอย่างทั้งหมด
        demo_general_deobfuscator()
        demo_luraph_deobfuscator()
        compare_methods()
        analyze_obfuscation_techniques()
        
        print("\n" + "=" * 50)
        print("เสร็จสิ้นการทดสอบ!")
        print("ใช้ --interactive สำหรับโหมดโต้ตอบ")
