# Lua Deobfuscator

เครื่องมือสำหรับ deobfuscate ไฟล์ Lua ที่ถูก obfuscate ด้วย obfuscator ต่างๆ รองรับ Luraph Obfuscator และ obfuscator อื่นๆ

## ไฟล์ในโปรเจค

- `enhanced_safe_deobfuscator.py` - **แนะนำสูงสุด** ⭐ Enhanced deobfuscator (decode string ได้มาก, หา hidden code, ไม่มี tab)
- `safe_luraph_deobfuscator.py` - Safe deobfuscator (string ไม่พัง, ปลอดภัย)
- `lua_deobfuscator.py` - General <PERSON><PERSON> deobfuscator สำหรับ obfuscator ทั่วไป
- `luraph_deobfuscator.py` - Aggressive deobfuscator (แปลงได้มาก แต่อาจมี string พัง)
- `deobfuscate.bat` - Batch script สำหรับ Windows (ใช้ Enhanced เป็นค่าเริ่มต้น)
- `compare_results.py` - เปรียบเทียบผลลัพธ์ของ deobfuscator ต่างๆ
- `obfuscated.lua` - ไฟล์ตัวอย่างที่ถูก obfuscate

## การติดตั้ง

1. ต้องมี Python 3.6+ ติดตั้งในระบบ
2. ดาวน์โหลดไฟล์ทั้งหมดมาไว้ในโฟลเดอร์เดียวกัน

## วิธีการใช้งาน

### วิธีที่ 1: ใช้ Batch Script (แนะนำสำหรับ Windows)

```batch
# Deobfuscate ไฟล์ (output จะเป็น deobfuscated.lua)
deobfuscate.bat obfuscated.lua

# Deobfuscate ไฟล์และกำหนดชื่อ output
deobfuscate.bat obfuscated.lua clean_code.lua
```

### วิธีที่ 2: ใช้ Python Script โดยตรง

#### Enhanced Safe Deobfuscator (แนะนำสูงสุด) ⭐
```bash
# สำหรับไฟล์ที่ถูก obfuscate ด้วย Luraph - คุณภาพสูงสุด
# - Decode string ได้มากขึ้น
# - หาและ decode hidden code blocks
# - จัดรูปแบบ code ไม่มี tab
python enhanced_safe_deobfuscator.py obfuscated.lua clean.lua
```

#### Safe Deobfuscator (ปลอดภัย)
```bash
# สำหรับไฟล์ที่ถูก obfuscate ด้วย Luraph - string ไม่พัง
python safe_luraph_deobfuscator.py obfuscated.lua clean.lua
```

#### General Deobfuscator
```bash
# การใช้งานพื้นฐาน
python lua_deobfuscator.py obfuscated.lua

# กำหนด output file และจัดรูปแบบ code
python lua_deobfuscator.py obfuscated.lua -o clean.lua -f
```

#### Luraph Deobfuscator (แปลงได้มาก แต่อาจมี string พัง)
```bash
# สำหรับไฟล์ที่ถูก obfuscate ด้วย Luraph
python luraph_deobfuscator.py obfuscated.lua clean.lua
```

## คุณสมบัติ

### General Deobfuscator (`lua_deobfuscator.py`)
- ตรวจสอบประเภท obfuscator อัตโนมัติ
- แปลง hex encoded strings (`\x41` → `A`)
- แปลง unicode encoded strings (`\u{41}` → `A`)
- แปลง octal encoded strings (`\101` → `A`)
- ลบ comment และ whitespace ที่ไม่จำเป็น
- จัดรูปแบบ code ให้อ่านง่าย

### Luraph Deobfuscator (`luraph_deobfuscator.py`)
- เฉพาะสำหรับ Luraph Obfuscator v14.4.1+
- แปลง string escape sequences ทุกประเภท
- แปลง hex numbers เป็น decimal
- แปลง binary numbers เป็ decimal
- วิเคราะห์โครงสร้างของ Luraph
- ทำความสะอาด obfuscated variable names
- จัดรูปแบบ code พร้อม indentation

## ตัวอย่างการใช้งาน

### ก่อน Deobfuscate
```lua
-- This file was protected using Luraph Obfuscator v14.4.1
return({Bp=function(C,C,M)C[0B10]=M;end,pp=function(C,C)return{C};end...
```

### หลัง Deobfuscate
```lua
return({
    Bp = function(var_1, var_1, var_3)
        var_1[2] = var_3;
    end,
    pp = function(var_1, var_1)
        return {var_1};
    end,
    ...
})
```

## การทำงานของ Deobfuscator

1. **ตรวจสอบประเภท Obfuscator** - วิเคราะห์ไฟล์เพื่อระบุประเภท obfuscator
2. **แปลง String Encoding** - แปลง hex, unicode, octal strings
3. **แปลง Number Encoding** - แปลง hex และ binary numbers
4. **ทำความสะอาด Code** - ลบ comment และ whitespace ที่ไม่จำเป็น
5. **ปรับปรุง Variable Names** - แทนที่ชื่อตัวแปรที่สับสน
6. **จัดรูปแบบ Code** - เพิ่ม indentation และ formatting

## ข้อจำกัด

- Luraph Obfuscator เป็น obfuscator ที่ซับซ้อนมาก การ deobfuscate อาจไม่สมบูรณ์ 100%
- บาง obfuscator ใช้เทคนิคที่ซับซ้อนเกินไป อาจต้องการการ deobfuscate ด้วยมือ
- ไฟล์ที่มีการ encrypt หรือ compress อาจต้องการเครื่องมือเพิ่มเติม

## การแก้ไขปัญหา

### Python ไม่พบ
- ติดตั้ง Python จาก https://python.org
- ตรวจสอบว่า Python อยู่ใน PATH

### Encoding Error
- ลองเปลี่ยน encoding ในไฟล์ Python
- ใช้ text editor ที่รองรับ UTF-8

### ผลลัพธ์ไม่สมบูรณ์
- ลองใช้ deobfuscator ตัวอื่น
- ทำการ deobfuscate ด้วยมือเพิ่มเติม

## การพัฒนาเพิ่มเติม

หากต้องการเพิ่มการรองรับ obfuscator ใหม่:

1. เพิ่ม detection pattern ใน `detect_obfuscator()`
2. สร้าง deobfuscation function ใหม่
3. เพิ่มการเรียกใช้ใน `deobfuscate()` method

## License

MIT License - ใช้งานได้อย่างอิสระ

## ข้อควรระวัง

- ใช้เครื่องมือนี้เฉพาะกับไฟล์ที่คุณมีสิทธิ์เท่านั้น
- การ deobfuscate อาจผิดกฎหมายในบางกรณี
- ผลลัพธ์อาจไม่สมบูรณ์ ควรตรวจสอบก่อนใช้งาน
