#!/usr/bin/env python3
"""
Improved Luraph Deobfuscator - แก้ไขปัญหาการ deobfuscate string
"""

import re
import sys
import os
from typing import Dict, List, Tuple, Optional

class ImprovedLuraphDeobfuscator:
    def __init__(self):
        self.constants = {}
        self.functions = {}
        self.variables = {}
        
    def read_file(self, filepath: str) -> str:
        """อ่านไฟล์ Lua"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            with open(filepath, 'r', encoding='latin-1') as f:
                return f.read()
    
    def write_file(self, filepath: str, content: str):
        """เขียนไฟล์ที่ deobfuscate แล้ว"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def safe_decode_string_escapes(self, content: str) -> str:
        """แปลง string escape sequences อย่างปลอดภัย - เฉพาะใน string literals"""
        
        def process_string_literal(match):
            """ประมวลผล string literal แต่ละตัว"""
            full_match = match.group(0)
            quote_char = full_match[0]  # ' หรือ "
            string_content = full_match[1:-1]  # เนื้อหาใน string (ไม่รวม quotes)
            
            # สำรอง string เดิมไว้ในกรณีที่เกิดข้อผิดพลาด
            original_content = string_content
            
            try:
                # แปลง escape sequences ที่ปลอดภัย
                def safe_hex_replacer(m):
                    try:
                        hex_val = m.group(1)
                        if len(hex_val) == 2:  # ต้องเป็น 2 หลักเท่านั้น
                            char_code = int(hex_val, 16)
                            if 32 <= char_code <= 126 or char_code in [9, 10, 13]:  # printable + tab/newline/cr
                                return chr(char_code)
                    except (ValueError, OverflowError):
                        pass
                    return m.group(0)  # คืนค่าเดิมถ้าแปลงไม่ได้
                
                def safe_unicode_replacer(m):
                    try:
                        unicode_val = m.group(1)
                        char_code = int(unicode_val, 16)
                        if char_code <= 0x10FFFF:  # Unicode range ที่ถูกต้อง
                            return chr(char_code)
                    except (ValueError, OverflowError):
                        pass
                    return m.group(0)
                
                def safe_octal_replacer(m):
                    try:
                        octal_val = m.group(1)
                        if len(octal_val) <= 3 and all(c in '01234567' for c in octal_val):
                            char_code = int(octal_val, 8)
                            if char_code <= 255:
                                return chr(char_code)
                    except (ValueError, OverflowError):
                        pass
                    return m.group(0)
                
                # แปลงเฉพาะ escape sequences ที่มั่นใจว่าถูกต้อง
                string_content = re.sub(r'\\x([0-9a-fA-F]{2})', safe_hex_replacer, string_content)
                string_content = re.sub(r'\\u\{([0-9a-fA-F]+)\}', safe_unicode_replacer, string_content)
                string_content = re.sub(r'\\([0-7]{1,3})', safe_octal_replacer, string_content)
                
                # แปลง escape sequences พื้นฐานที่ปลอดภัย
                basic_escapes = {
                    '\\\\': '\\',
                    '\\"': '"',
                    "\\'": "'",
                    '\\n': '\n',
                    '\\r': '\r',
                    '\\t': '\t',
                    '\\a': '\a',
                    '\\b': '\b',
                    '\\f': '\f',
                    '\\v': '\v'
                }
                
                for escape, replacement in basic_escapes.items():
                    string_content = string_content.replace(escape, replacement)
                
                # จัดการ \z (skip whitespace in Lua)
                string_content = re.sub(r'\\z\s*', '', string_content)
                
            except Exception:
                # ถ้าเกิดข้อผิดพลาด ให้ใช้ string เดิม
                string_content = original_content
            
            return quote_char + string_content + quote_char
        
        # หา string literals และประมวลผลแต่ละตัว
        # Pattern ที่ปลอดภัยสำหรับ string literals
        single_quote_pattern = r"'([^'\\]|\\.)*'"
        double_quote_pattern = r'"([^"\\]|\\.)*"'
        
        # ประมวลผล single-quoted strings
        content = re.sub(single_quote_pattern, process_string_literal, content)
        
        # ประมวลผล double-quoted strings
        content = re.sub(double_quote_pattern, process_string_literal, content)
        
        return content
    
    def safe_simplify_numbers(self, content: str) -> str:
        """แปลง hex และ binary numbers เป็น decimal อย่างปลอดภัย"""
        
        def safe_hex_to_decimal(match):
            try:
                hex_str = match.group(0)
                decimal = int(hex_str, 16)
                # เฉพาะตัวเลขที่ไม่ใหญ่เกินไป
                if decimal <= 1000000:
                    return str(decimal)
            except (ValueError, OverflowError):
                pass
            return match.group(0)
        
        def safe_binary_to_decimal(match):
            try:
                binary_str = match.group(0)
                binary_digits = binary_str[2:].replace('_', '')  # ลบ 0b และ _
                decimal = int(binary_digits, 2)
                # เฉพาะตัวเลขที่ไม่ใหญ่เกินไป
                if decimal <= 1000000:
                    return str(decimal)
            except (ValueError, OverflowError):
                pass
            return match.group(0)
        
        # แปลง hex numbers (0x...)
        content = re.sub(r'0[xX][0-9a-fA-F]+', safe_hex_to_decimal, content)
        
        # แปลง binary numbers (0b...)
        content = re.sub(r'0[bB][01_]+', safe_binary_to_decimal, content)
        
        return content
    
    def conservative_clean_names(self, content: str) -> str:
        """ทำความสะอาด variable names อย่างระมัดระวัง"""
        # เฉพาะ single character variables ที่มั่นใจว่าเป็น obfuscated
        confusing_vars = {
            # เฉพาะตัวแปรที่เป็น parameter ของ function เท่านั้น
        }
        
        # หา function parameters และแทนที่เฉพาะใน scope นั้น
        def replace_in_function(match):
            func_def = match.group(0)
            # ไม่แทนที่ตัวแปรเพื่อความปลอดภัย
            return func_def
        
        # ไม่แทนที่ตัวแปรเพื่อความปลอดภัย
        return content
    
    def format_code_safely(self, content: str) -> str:
        """จัดรูปแบบ code อย่างระมัดระวัง"""
        # เพิ่ม newlines ในจุดที่ปลอดภัย
        content = re.sub(r';(?=\s*[a-zA-Z_])', ';\n', content)  # หลัง ; ที่ตามด้วยตัวอักษร
        content = re.sub(r'\{(?=\s*[a-zA-Z_])', '{\n', content)  # หลัง { ที่ตามด้วยตัวอักษร
        content = re.sub(r'(?<=[a-zA-Z_])\}', '\n}', content)    # ก่อน } ที่หลังตัวอักษร
        
        # เพิ่ม newlines หลัง keywords
        lua_keywords = ['do', 'then', 'else', 'elseif']
        for keyword in lua_keywords:
            content = re.sub(rf'\b{keyword}\b(?=\s*[a-zA-Z_])', f'{keyword}\n', content)
        
        # เพิ่ม newlines ก่อน end
        content = re.sub(r'(?<=[a-zA-Z_;}])\s*\bend\b', '\nend', content)
        
        # ลบ empty lines ที่เกินจำเป็น
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        return content
    
    def deobfuscate(self, content: str) -> str:
        """Main deobfuscation process ที่ปรับปรุงแล้ว"""
        print("เริ่มต้น Improved Luraph deobfuscation...")
        
        # ขั้นตอนที่ 1: ลบ comment header
        print("1. กำลังลบ obfuscator header...")
        content = re.sub(r'-- This file was protected using.*?\n', '', content)
        
        # ขั้นตอนที่ 2: แปลง string escapes อย่างปลอดภัย
        print("2. กำลังแปลง string escape sequences อย่างปลอดภัย...")
        content = self.safe_decode_string_escapes(content)
        
        # ขั้นตอนที่ 3: แปลง numbers อย่างปลอดภัย
        print("3. กำลังแปลง hex และ binary numbers...")
        content = self.safe_simplify_numbers(content)
        
        # ขั้นตอนที่ 4: จัดรูปแบบ code อย่างระมัดระวัง
        print("4. กำลังจัดรูปแบบ code...")
        content = self.format_code_safely(content)
        
        print("5. เสร็จสิ้น!")
        
        return content

def main():
    if len(sys.argv) < 2:
        print("การใช้งาน: python improved_luraph_deobfuscator.py <input_file> [output_file]")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else 'improved_deobfuscated.lua'
    
    if not os.path.exists(input_file):
        print(f"ไม่พบไฟล์: {input_file}")
        sys.exit(1)
    
    deobfuscator = ImprovedLuraphDeobfuscator()
    
    print(f"กำลังอ่านไฟล์: {input_file}")
    content = deobfuscator.read_file(input_file)
    
    # ตรวจสอบว่าเป็น Luraph หรือไม่
    if "Luraph Obfuscator" not in content:
        print("คำเตือน: ไฟล์นี้อาจไม่ได้ถูก obfuscate ด้วย Luraph")
    
    deobfuscated_content = deobfuscator.deobfuscate(content)
    
    print(f"กำลังเขียนไฟล์: {output_file}")
    deobfuscator.write_file(output_file, deobfuscated_content)
    
    print("เสร็จสิ้น!")
    print(f"ไฟล์ที่ deobfuscate แล้ว: {output_file}")

if __name__ == "__main__":
    main()
