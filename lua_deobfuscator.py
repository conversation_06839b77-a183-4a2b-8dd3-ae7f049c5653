#!/usr/bin/env python3
"""
Lua Deobfuscator - สำหรับ deobfuscate ไฟล์ Lua ที่ถูก obfuscate
รองรับ Luraph Obfuscator และ obfuscator อื่นๆ
"""

import re
import sys
import os
import argparse
from typing import Dict, List, Tuple, Optional

class LuaDeobfuscator:
    def __init__(self):
        self.string_patterns = []
        self.variable_mappings = {}
        self.function_mappings = {}
        
    def read_file(self, filepath: str) -> str:
        """อ่านไฟล์ Lua"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # ลองใช้ encoding อื่น
            with open(filepath, 'r', encoding='latin-1') as f:
                return f.read()
    
    def write_file(self, filepath: str, content: str):
        """เขียนไฟล์ที่ deobfuscate แล้ว"""
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def detect_obfuscator(self, content: str) -> str:
        """ตรวจสอบประเภทของ obfuscator"""
        if "Luraph Obfuscator" in content:
            return "luraph"
        elif "loadstring" in content and "string.char" in content:
            return "basic_string"
        elif re.search(r'\\x[0-9a-fA-F]{2}', content):
            return "hex_encoded"
        elif re.search(r'\\u\{[0-9a-fA-F]+\}', content):
            return "unicode_encoded"
        else:
            return "unknown"
    
    def decode_hex_strings(self, content: str) -> str:
        """แปลง hex encoded strings"""
        def hex_replacer(match):
            hex_str = match.group(1)
            try:
                return chr(int(hex_str, 16))
            except ValueError:
                return match.group(0)
        
        # แปลง \x hex codes
        content = re.sub(r'\\x([0-9a-fA-F]{2})', hex_replacer, content)
        return content
    
    def decode_unicode_strings(self, content: str) -> str:
        """แปลง unicode encoded strings"""
        def unicode_replacer(match):
            unicode_str = match.group(1)
            try:
                return chr(int(unicode_str, 16))
            except ValueError:
                return match.group(0)
        
        # แปลง \u{} unicode codes
        content = re.sub(r'\\u\{([0-9a-fA-F]+)\}', unicode_replacer, content)
        return content
    
    def decode_octal_strings(self, content: str) -> str:
        """แปลง octal encoded strings"""
        def octal_replacer(match):
            octal_str = match.group(1)
            try:
                return chr(int(octal_str, 8))
            except ValueError:
                return match.group(0)
        
        # แปลง \ddd octal codes
        content = re.sub(r'\\([0-7]{1,3})', octal_replacer, content)
        return content
    
    def clean_whitespace_and_comments(self, content: str) -> str:
        """ลบ whitespace และ comment ที่ไม่จำเป็น"""
        # ลบ comment แบบ --
        content = re.sub(r'--[^\r\n]*', '', content)
        
        # ลบ comment แบบ --[[ ]]
        content = re.sub(r'--\[\[.*?\]\]', '', content, flags=re.DOTALL)
        
        # ลบ whitespace ที่เกินจำเป็น
        content = re.sub(r'\s+', ' ', content)
        
        return content.strip()
    
    def extract_string_literals(self, content: str) -> List[str]:
        """แยก string literals ออกมา"""
        strings = []
        
        # หา string ที่อยู่ใน quotes
        string_pattern = r'["\']([^"\'\\]|\\.)*["\']'
        matches = re.findall(string_pattern, content)
        strings.extend(matches)
        
        return strings
    
    def deobfuscate_luraph(self, content: str) -> str:
        """Deobfuscate Luraph obfuscated code"""
        print("ตรวจพบ Luraph Obfuscator - กำลังพยายาม deobfuscate...")
        
        # ขั้นตอนพื้นฐานสำหรับ Luraph
        # 1. แปลง encoded strings
        content = self.decode_hex_strings(content)
        content = self.decode_unicode_strings(content)
        content = self.decode_octal_strings(content)
        
        # 2. ลบ comment header
        content = re.sub(r'-- This file was protected using.*?\n', '', content)
        
        # 3. พยายามหา pattern ของ return statement
        if content.startswith('return('):
            print("พบ return statement pattern")
            # ลองแยกส่วนต่างๆ ออกมา
            
        return content
    
    def deobfuscate_basic(self, content: str) -> str:
        """Deobfuscate basic obfuscation"""
        print("กำลัง deobfuscate basic obfuscation...")
        
        # แปลง encoded strings
        content = self.decode_hex_strings(content)
        content = self.decode_unicode_strings(content)
        content = self.decode_octal_strings(content)
        
        # ลบ whitespace และ comment ที่ไม่จำเป็น
        content = self.clean_whitespace_and_comments(content)
        
        return content
    
    def deobfuscate(self, content: str) -> str:
        """Main deobfuscation function"""
        obfuscator_type = self.detect_obfuscator(content)
        print(f"ตรวจพบ obfuscator ประเภท: {obfuscator_type}")
        
        if obfuscator_type == "luraph":
            return self.deobfuscate_luraph(content)
        elif obfuscator_type in ["basic_string", "hex_encoded", "unicode_encoded"]:
            return self.deobfuscate_basic(content)
        else:
            print("ไม่สามารถระบุประเภท obfuscator ได้ - ใช้วิธี basic deobfuscation")
            return self.deobfuscate_basic(content)
    
    def format_lua_code(self, content: str) -> str:
        """จัดรูปแบบ Lua code ให้อ่านง่าย"""
        # เพิ่ม newline หลัง semicolon
        content = re.sub(r';', ';\n', content)
        
        # เพิ่ม newline หลัง closing brace
        content = re.sub(r'}', '}\n', content)
        
        # เพิ่ม newline หลัง 'end'
        content = re.sub(r'\bend\b', 'end\n', content)
        
        # เพิ่ม newline หลัง 'do'
        content = re.sub(r'\bdo\b', 'do\n', content)
        
        # เพิ่ม newline หลัง 'then'
        content = re.sub(r'\bthen\b', 'then\n', content)
        
        # ลบ empty lines ที่เกินจำเป็น
        content = re.sub(r'\n\s*\n', '\n', content)
        
        return content

def main():
    parser = argparse.ArgumentParser(description='Lua Deobfuscator')
    parser.add_argument('input_file', help='ไฟล์ Lua ที่ถูก obfuscate')
    parser.add_argument('-o', '--output', help='ไฟล์ output (default: deobfuscated.lua)')
    parser.add_argument('-f', '--format', action='store_true', help='จัดรูปแบบ code ให้อ่านง่าย')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_file):
        print(f"ไม่พบไฟล์: {args.input_file}")
        sys.exit(1)
    
    output_file = args.output or 'deobfuscated.lua'
    
    deobfuscator = LuaDeobfuscator()
    
    print(f"กำลังอ่านไฟล์: {args.input_file}")
    content = deobfuscator.read_file(args.input_file)
    
    print("กำลัง deobfuscate...")
    deobfuscated_content = deobfuscator.deobfuscate(content)
    
    if args.format:
        print("กำลังจัดรูปแบบ code...")
        deobfuscated_content = deobfuscator.format_lua_code(deobfuscated_content)
    
    print(f"กำลังเขียนไฟล์: {output_file}")
    deobfuscator.write_file(output_file, deobfuscated_content)
    
    print("เสร็จสิ้น!")
    print(f"ไฟล์ที่ deobfuscate แล้ว: {output_file}")

if __name__ == "__main__":
    main()
