@echo off
echo ========================================
echo     Lua Deobfuscator Tool
echo ========================================
echo.

if "%~1"=="" (
    echo Usage: deobfuscate.bat ^<input_file^> [output_file]
    echo.
    echo Examples:
    echo   deobfuscate.bat obfuscated.lua
    echo   deobfuscate.bat obfuscated.lua clean.lua
    echo.
    pause
    exit /b 1
)

set INPUT_FILE=%~1
set OUTPUT_FILE=%~2

if not exist "%INPUT_FILE%" (
    echo Error: File not found: %INPUT_FILE%
    pause
    exit /b 1
)

if "%OUTPUT_FILE%"=="" (
    set OUTPUT_FILE=deobfuscated.lua
)

echo Input file: %INPUT_FILE%
echo Output file: %OUTPUT_FILE%
echo.

echo Checking obfuscator type...
findstr /C:"Luraph Obfuscator" "%INPUT_FILE%" >nul
if %errorlevel%==0 (
    echo Detected: Luraph Obfuscator
    echo Using Luraph deobfuscator...
    python luraph_deobfuscator.py "%INPUT_FILE%" "%OUTPUT_FILE%"
) else (
    echo Using general deobfuscator...
    python lua_deobfuscator.py "%INPUT_FILE%" -o "%OUTPUT_FILE%" -f
)

if %errorlevel%==0 (
    echo.
    echo Success!
    echo Deobfuscated file: %OUTPUT_FILE%
) else (
    echo.
    echo Error occurred!
)

echo.
pause
